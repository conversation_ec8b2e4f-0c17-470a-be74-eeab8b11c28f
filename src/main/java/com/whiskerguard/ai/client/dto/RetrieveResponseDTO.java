package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * RetrieveResponseDTO
 *
 * 向量检索接口返回的数据结构，封装了多条匹配结果。
 * 支持元数据和格式化上下文。
 */
public class RetrieveResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检索结果列表，每项包含文本片段和相似度分数
     */
    @NotNull
    private List<Result> results;

    /**
     * 合并后的上下文，根据请求参数决定是否返回
     */
    private String mergedContext;

    /**
     * 上下文格式，RAW/MARKDOWN/HTML
     */
    private String contextFormat;

    public RetrieveResponseDTO() {}

    public RetrieveResponseDTO(List<Result> results) {
        this.results = results;
        this.mergedContext = null;
        this.contextFormat = "RAW";
    }

    public RetrieveResponseDTO(List<Result> results, String mergedContext, String contextFormat) {
        this.results = results;
        this.mergedContext = mergedContext;
        this.contextFormat = contextFormat;
    }

    public List<Result> getResults() {
        return results;
    }

    public void setResults(List<Result> results) {
        this.results = results;
    }

    public String getMergedContext() {
        return mergedContext;
    }

    public void setMergedContext(String mergedContext) {
        this.mergedContext = mergedContext;
    }

    public String getContextFormat() {
        return contextFormat;
    }

    public void setContextFormat(String contextFormat) {
        this.contextFormat = contextFormat;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RetrieveResponseDTO)) return false;
        RetrieveResponseDTO that = (RetrieveResponseDTO) o;
        return (
            Objects.equals(results, that.results) &&
            Objects.equals(mergedContext, that.mergedContext) &&
            Objects.equals(contextFormat, that.contextFormat)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(results, mergedContext, contextFormat);
    }

    @Override
    public String toString() {
        return (
            "RetrieveResponseDTO{" +
            "results=" +
            results +
            ", mergedContext='" +
            mergedContext +
            '\'' +
            ", contextFormat='" +
            contextFormat +
            '\'' +
            "}"
        );
    }

    /**
     * Result
     *
     * 嵌套静态类，描述单条检索结果对象。
     */
    public static class Result implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 文本片段 */
        @NotBlank
        @Size(max = 10000)
        private String text;

        /** 相似度分数 */
        @NotNull
        private Float score;

        /** 文档ID */
        private String documentId;

        /** 切片ID */
        private Integer chunkId;

        /** 元数据信息 */
        private Map<String, Object> metadata;

        public Result() {}

        public Result(String text, Float score) {
            this.text = text;
            this.score = score;
            this.documentId = null;
            this.chunkId = null;
            this.metadata = null;
        }

        public Result(String text, Float score, String documentId, Integer chunkId, Map<String, Object> metadata) {
            this.text = text;
            this.score = score;
            this.documentId = documentId;
            this.chunkId = chunkId;
            this.metadata = metadata;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public Float getScore() {
            return score;
        }

        public void setScore(Float score) {
            this.score = score;
        }

        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public Integer getChunkId() {
            return chunkId;
        }

        public void setChunkId(Integer chunkId) {
            this.chunkId = chunkId;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Result)) return false;
            Result result = (Result) o;
            return (
                Objects.equals(text, result.text) &&
                Objects.equals(score, result.score) &&
                Objects.equals(documentId, result.documentId) &&
                Objects.equals(chunkId, result.chunkId) &&
                Objects.equals(metadata, result.metadata)
            );
        }

        @Override
        public int hashCode() {
            return Objects.hash(text, score, documentId, chunkId, metadata);
        }

        @Override
        public String toString() {
            return (
                "Result{" +
                "text='" +
                text +
                '\'' +
                ", score=" +
                score +
                ", documentId='" +
                documentId +
                '\'' +
                ", chunkId=" +
                chunkId +
                ", metadata=" +
                metadata +
                "}"
            );
        }
    }
}
