/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：EntityMapper.java
 * 包    名：com.whiskerguard.ai.service.mapper
 * 描    述：通用实体映射器接口，定义DTO与实体对象之间的通用转换方法
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.mapper;

import java.util.List;
import org.mapstruct.BeanMapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 通用实体映射器接口
 * <p>
 * Contract for a generic dto to entity mapper.
 * <p>
 * 定义DTO与实体对象之间的通用转换方法，为所有具体的映射器提供基础功能。
 *
 * @param <D> DTO类型参数。DTO type parameter.
 * @param <E> 实体类型参数。Entity type parameter.
 */
public interface EntityMapper<D, E> {
    /**
     * 将DTO转换为实体对象
     * <p>
     * Convert DTO to entity.
     *
     * @param dto DTO对象
     * @return 转换后的实体对象
     */
    E toEntity(D dto);

    /**
     * 将实体对象转换为DTO
     * <p>
     * Convert entity to DTO.
     *
     * @param entity 实体对象
     * @return 转换后的DTO对象
     */
    D toDto(E entity);

    /**
     * 将DTO列表转换为实体对象列表
     * <p>
     * Convert DTOs to entities.
     *
     * @param dtoList DTO对象列表
     * @return 转换后的实体对象列表
     */
    List<E> toEntity(List<D> dtoList);

    /**
     * 将实体对象列表转换为DTO列表
     * <p>
     * Convert entities to DTOs.
     *
     * @param entityList 实体对象列表
     * @return 转换后的DTO对象列表
     */
    List<D> toDto(List<E> entityList);

    /**
     * 部分更新实体对象
     * <p>
     * 使用DTO中的非空属性更新实体对象，忽略DTO中的空值属性。
     * <p>
     * Partially update entity with properties from DTO, ignoring null properties.
     *
     * @param entity 目标实体对象。target entity to update.
     * @param dto 包含更新值的DTO对象。source DTO containing updates.
     */
    @Named("partialUpdate")
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget E entity, D dto);
}
