/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiToolMetricsServiceImpl.java
 * 包    名：com.whiskerguard.ai.service.impl
 * 描    述：AI工具指标管理服务实现类，处理AI工具指标的业务逻辑
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.AiToolMetrics;
import com.whiskerguard.ai.domain.enumeration.MetricsPeriod;
import com.whiskerguard.ai.repository.AiToolMetricsRepository;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.dto.AiToolMetricsDTO;
import com.whiskerguard.ai.service.mapper.AiToolMetricsMapper;
import com.whiskerguard.ai.service.workflow.AiWorkflowResult;
import java.time.Instant;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI工具指标管理服务实现类
 * <p>
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.AiToolMetrics}.
 * <p>
 * 实现AI工具指标的创建、查询、更新和删除等功能，提供分页查询和按条件筛选的能力。
 * 支持工具使用情况统计和性能指标分析。
 */
@Service
@Transactional
public class AiToolMetricsServiceImpl implements AiToolMetricsService {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiToolMetricsServiceImpl.class);

    /**
     * AI工具指标仓库接口，用于数据库操作
     */
    private final AiToolMetricsRepository aiToolMetricsRepository;

    /**
     * AI工具仓库接口，用于查询工具信息
     */
    private final AiToolRepository aiToolRepository;

    /**
     * AI工具指标对象映射器，用于DTO与实体的转换
     */
    private final AiToolMetricsMapper aiToolMetricsMapper;

    /**
     * 构造函数，通过依赖注入获取所需的组件
     *
     * @param aiToolMetricsRepository AI工具指标仓库，用于数据访问
     * @param aiToolRepository AI工具仓库，用于查询工具信息
     * @param aiToolMetricsMapper AI工具指标映射器，用于对象转换
     */
    public AiToolMetricsServiceImpl(
        AiToolMetricsRepository aiToolMetricsRepository,
        AiToolRepository aiToolRepository,
        AiToolMetricsMapper aiToolMetricsMapper
    ) {
        this.aiToolMetricsRepository = aiToolMetricsRepository;
        this.aiToolRepository = aiToolRepository;
        this.aiToolMetricsMapper = aiToolMetricsMapper;
    }

    /**
     * 保存AI工具指标
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiToolMetricsDTO save(AiToolMetricsDTO aiToolMetricsDTO) {
        LOG.debug("Request to save AiToolMetrics : {}", aiToolMetricsDTO);
        AiToolMetrics aiToolMetrics = aiToolMetricsMapper.toEntity(aiToolMetricsDTO);
        aiToolMetrics = aiToolMetricsRepository.save(aiToolMetrics);
        return aiToolMetricsMapper.toDto(aiToolMetrics);
    }

    /**
     * 更新AI工具指标
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiToolMetricsDTO update(AiToolMetricsDTO aiToolMetricsDTO) {
        LOG.debug("Request to update AiToolMetrics : {}", aiToolMetricsDTO);
        AiToolMetrics aiToolMetrics = aiToolMetricsMapper.toEntity(aiToolMetricsDTO);
        aiToolMetrics = aiToolMetricsRepository.save(aiToolMetrics);
        return aiToolMetricsMapper.toDto(aiToolMetrics);
    }

    /**
     * 部分更新AI工具指标
     * <p>
     * {@inheritDoc}
     */
    @Override
    public Optional<AiToolMetricsDTO> partialUpdate(AiToolMetricsDTO aiToolMetricsDTO) {
        LOG.debug("Request to partially update AiToolMetrics : {}", aiToolMetricsDTO);

        return aiToolMetricsRepository
            .findById(aiToolMetricsDTO.getId())
            .map(existingAiToolMetrics -> {
                aiToolMetricsMapper.partialUpdate(existingAiToolMetrics, aiToolMetricsDTO);

                return existingAiToolMetrics;
            })
            .map(aiToolMetricsRepository::save)
            .map(aiToolMetricsMapper::toDto);
    }

    /**
     * 查询所有AI工具指标
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiToolMetricsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AiToolMetrics");
        return aiToolMetricsRepository.findAll(pageable).map(aiToolMetricsMapper::toDto);
    }

    /**
     * 根据ID查询AI工具指标
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<AiToolMetricsDTO> findOne(Long id) {
        LOG.debug("Request to get AiToolMetrics : {}", id);
        return aiToolMetricsRepository.findById(id).map(aiToolMetricsMapper::toDto);
    }

    /**
     * 根据ID删除AI工具指标
     * <p>
     * {@inheritDoc}
     */
    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AiToolMetrics : {}", id);
        aiToolMetricsRepository.deleteById(id);
    }

    /**
     * 保存AI调用指标
     *
     * @param toolKey 工具标识
     * @param result AI工作流结果
     * @param request AI请求信息
     */
    @Override
    public void saveAiCallMetrics(String toolKey, AiWorkflowResult result, AiRequest request) {
        LOG.debug("开始保存AI调用指标，工具: {}, 请求ID: {}", toolKey, request.getId());

        try {
            // 查找AI工具配置
            AiTool tool = aiToolRepository
                .findByToolKey(toolKey)
                .orElseThrow(() -> new IllegalArgumentException("Tool not found: " + toolKey));

            // 创建指标记录
            AiToolMetrics metrics = createBaseMetrics(tool, request.getTenantId(), request.getResponseTime());
            metrics.setResponseTime((int) result.getDurationMs());
            metrics.setTotalRequests(1L);
            metrics.setSuccessCount(1L);
            metrics.setFailureCount(0L);
            metrics.setErrorRate(0f);

            aiToolMetricsRepository.save(metrics);
            LOG.debug("已保存AI调用指标，工具: {}, 响应时间: {}ms", toolKey, result.getDurationMs());
        } catch (Exception e) {
            LOG.error("保存AI调用指标失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 保存流式AI调用指标
     *
     * @param toolKey 工具标识
     * @param tenantId 租户ID
     * @param request AI请求信息
     * @param responseTime 响应时间
     */
    @Override
    public void saveStreamCallMetrics(String toolKey, Long tenantId, AiRequest request, Instant responseTime) {
        LOG.debug("开始保存流式AI调用指标，工具: {}, 请求ID: {}", toolKey, request.getId());

        try {
            // 计算响应时间
            long durationMs = responseTime.toEpochMilli() - request.getRequestTime().toEpochMilli();

            // 查找AI工具配置
            AiTool tool = aiToolRepository.findByToolKeyAndIsDeletedFalse(toolKey).orElse(null);

            if (tool == null) {
                LOG.warn("未找到工具配置: {}，跳过指标保存", toolKey);
                return;
            }

            // 创建指标记录
            AiToolMetrics metrics = createBaseMetrics(tool, tenantId, responseTime);
            metrics.setResponseTime((int) durationMs);
            metrics.setTotalRequests(1L);
            metrics.setSuccessCount(1L);
            metrics.setFailureCount(0L);
            metrics.setErrorRate(0f);

            aiToolMetricsRepository.save(metrics);
            LOG.debug("已保存流式AI调用指标，工具: {}, 响应时间: {}ms", toolKey, durationMs);
        } catch (Exception e) {
            LOG.error("保存流式调用指标失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 创建基础指标记录
     */
    private AiToolMetrics createBaseMetrics(AiTool tool, Long tenantId, Instant collectDate) {
        AiToolMetrics metrics = new AiToolMetrics();
        metrics.setTool(tool);
        metrics.setTenantId(tenantId);
        metrics.setPeriod(MetricsPeriod.HOURLY);
        metrics.setCollectDate(collectDate);

        // 设置必需字段
        metrics.setVersion(1);
        metrics.setCreatedAt(Instant.now());
        metrics.setUpdatedAt(Instant.now());
        metrics.setIsDeleted(false);

        return metrics;
    }
}
