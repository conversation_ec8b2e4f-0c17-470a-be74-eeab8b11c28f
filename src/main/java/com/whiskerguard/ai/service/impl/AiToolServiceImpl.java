/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiToolServiceImpl.java
 * 包    名：com.whiskerguard.ai.service.impl
 * 描    述：AI工具管理服务实现类，处理AI工具的业务逻辑
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.AiToolService;
import com.whiskerguard.ai.service.dto.AiToolDTO;
import com.whiskerguard.ai.service.mapper.AiToolMapper;
import java.time.Instant;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI工具管理服务实现类
 * <p>
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.AiTool}.
 * <p>
 * 实现AI工具的创建、查询、更新和删除等功能，提供分页查询和按条件筛选的能力。
 */
@Service
@Transactional
public class AiToolServiceImpl implements AiToolService {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiToolServiceImpl.class);

    /**
     * AI工具仓库接口，用于数据库操作
     */
    private final AiToolRepository aiToolRepository;

    /**
     * AI工具对象映射器，用于DTO与实体的转换
     */
    private final AiToolMapper aiToolMapper;

    /**
     * 构造函数，通过依赖注入获取所需的组件
     *
     * @param aiToolRepository AI工具仓库，用于数据访问
     * @param aiToolMapper AI工具映射器，用于对象转换
     */
    public AiToolServiceImpl(AiToolRepository aiToolRepository, AiToolMapper aiToolMapper) {
        this.aiToolRepository = aiToolRepository;
        this.aiToolMapper = aiToolMapper;
    }

    /**
     * 保存AI工具
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiToolDTO save(AiToolDTO aiToolDTO) {
        LOG.debug("Request to save AiTool : {}", aiToolDTO);
        AiTool aiTool = aiToolMapper.toEntity(aiToolDTO);

        //如果创建时间为空，则设置为当前时间
        if (aiTool.getCreatedAt() == null) {
            aiTool.setCreatedAt(Instant.now());
        }
        if (aiTool.getUpdatedAt() == null) {
            aiTool.setUpdatedAt(aiTool.getCreatedAt()); //设置为当前的创建时间
        }
        aiTool = aiToolRepository.save(aiTool);
        return aiToolMapper.toDto(aiTool);
    }

    /**
     * 更新AI工具
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiToolDTO update(AiToolDTO aiToolDTO) {
        LOG.debug("Request to update AiTool : {}", aiToolDTO);
        AiTool aiTool = aiToolMapper.toEntity(aiToolDTO);
        aiTool = aiToolRepository.save(aiTool);
        return aiToolMapper.toDto(aiTool);
    }

    /**
     * 部分更新AI工具
     * <p>
     * {@inheritDoc}
     */
    @Override
    public Optional<AiToolDTO> partialUpdate(AiToolDTO aiToolDTO) {
        LOG.debug("Request to partially update AiTool : {}", aiToolDTO);

        return aiToolRepository
            .findById(aiToolDTO.getId())
            .map(existingAiTool -> {
                aiToolMapper.partialUpdate(existingAiTool, aiToolDTO);

                return existingAiTool;
            })
            .map(aiToolRepository::save)
            .map(aiToolMapper::toDto);
    }

    /**
     * 查询所有AI工具
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiToolDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AiTools");
        return aiToolRepository.findAll(pageable).map(aiToolMapper::toDto);
    }

    /**
     * 查询所有模型
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiToolDTO> findAllModels(Pageable pageable) {
        LOG.debug("Request to get all Models");
        return aiToolRepository.findByIsModelAndIsDeletedFalse(true, pageable).map(aiToolMapper::toDto);
    }

    /**
     * 根据类别查询模型
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiToolDTO> findModelsByCategory(String modelCategory, Pageable pageable) {
        LOG.debug("Request to get Models by category: {}", modelCategory);
        return aiToolRepository.findByIsModelAndModelCategoryAndIsDeletedFalse(true, modelCategory, pageable).map(aiToolMapper::toDto);
    }

    /**
     * 根据ID查询AI工具
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<AiToolDTO> findOne(Long id) {
        LOG.debug("Request to get AiTool : {}", id);
        return aiToolRepository.findById(id).map(aiToolMapper::toDto);
    }

    /**
     * 删除AI工具
     * <p>
     * {@inheritDoc}
     */
    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AiTool : {}", id);
        aiToolRepository.deleteById(id);
    }
}
