package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.SensitiveWordFilterService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.exception.AiInvocationException;
import com.whiskerguard.ai.service.mapper.AiRequestMapper;
import com.whiskerguard.ai.service.workflow.AiWorkflow;
import com.whiskerguard.ai.service.workflow.AiWorkflowResult;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import java.time.Instant;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI 调用主要业务服务：记录请求、触发工作流、保存响应和指标
 */
@Service
@Transactional
public class AiInvocationService {

    private static final Logger log = LoggerFactory.getLogger(AiInvocationService.class);

    private final AiRequestRepository requestRepo;
    private final AiRequestMapper requestMapper;
    private final AiToolMetricsService metricsService;
    private final ObjectMapper objectMapper;
    private final AiToolRepository toolRepo;
    private final WorkflowClient workflowClient;
    private final SensitiveWordFilterService sensitiveWordFilterService;

    @Value("${temporal.task-queue:AiTaskQueue}")
    private String taskQueue;

    public AiInvocationService(
        AiRequestRepository requestRepo,
        AiRequestMapper requestMapper,
        AiToolMetricsService metricsService,
        ObjectMapper objectMapper,
        AiToolRepository toolRepo,
        WorkflowClient workflowClient,
        SensitiveWordFilterService sensitiveWordFilterService
    ) {
        this.requestRepo = requestRepo;
        this.requestMapper = requestMapper;
        this.metricsService = metricsService;
        this.objectMapper = objectMapper;
        this.toolRepo = toolRepo;
        this.workflowClient = workflowClient;
        this.sensitiveWordFilterService = sensitiveWordFilterService;
    }

    /**
     * 发起 AI 调用，记录请求和响应，并保存指标
     * 使用事务确保数据一致性
     */
    @Transactional
    public AiRequestDTO invoke(AiInvocationRequestDTO dto) {
        log.info("开始处理AI调用请求，工具类型: {}, 员工ID: {}", dto.getToolKey(), dto.getEmployeeId());

        // 1. 参数验证
        validateInvocationRequest(dto);

        // 2. 查找AI工具配置
        AiTool aiTool = findAiToolByKey(dto.getToolKey(), dto);
        log.debug("找到AI工具配置: {}", aiTool.getName());

        // 3. 创建并持久化请求记录
        AiRequest req = createAiRequest(dto, aiTool);
        req = requestRepo.save(req);
        log.debug("已保存AI请求记录，ID: {}", req.getId());

        // 4. 通过 Temporal 工作流执行调用
        String workflowId = "ai-workflow-" + UUID.randomUUID();
        AiWorkflow workflow = workflowClient.newWorkflowStub(
            AiWorkflow.class,
            WorkflowOptions.newBuilder()
                .setTaskQueue(taskQueue)
                .setWorkflowId(workflowId)
                // 设置工作流执行超时为 15 分钟，确保有足够时间完成 AI 调用
                .setWorkflowExecutionTimeout(java.time.Duration.ofMinutes(15))
                // 设置工作流运行超时为 12 分钟
                .setWorkflowRunTimeout(java.time.Duration.ofMinutes(12))
                .build()
        );

        AiWorkflowResult result = null;
        try {
            // 5. 同步执行工作流
            log.info("开始执行AI工作流，工作流ID: {}", workflowId);
            result = workflow.execute(dto);

            // 6. 更新请求为成功状态
            updateRequestWithSuccess(req, result);
            log.info(
                "AI调用成功完成，请求ID: {}, 响应长度: {}",
                req.getId(),
                result.getContent() != null ? result.getContent().length() : 0
            );
        } catch (Exception ex) {
            // 7. 处理调用失败
            log.error("AI调用失败，请求ID: {}, 错误: {}", req.getId(), ex.getMessage(), ex);
            updateRequestWithError(req, ex);

            // 抛出业务异常，触发事务回滚
            throw new AiInvocationException("AI调用失败: " + ex.getMessage(), dto.getToolKey(), "WORKFLOW_EXECUTION_FAILED", ex);
        }

        // 8. 保存指标（只有成功时才保存）
        if (result != null) {
            try {
                metricsService.saveAiCallMetrics(dto.getToolKey(), result, req);
                log.debug("已保存AI调用指标");
            } catch (Exception ex) {
                log.warn("保存指标失败，但不影响主流程: {}", ex.getMessage());
            }
        }

        // 4. 返回 DTO
        return requestMapper.toDto(req);
    }

    /**
     * 验证调用请求参数
     */
    private void validateInvocationRequest(AiInvocationRequestDTO dto) {
        if (dto.getToolKey() == null || dto.getToolKey().trim().isEmpty()) {
            throw new IllegalArgumentException("工具类型不能为空");
        }
        if (dto.getPrompt() == null || dto.getPrompt().trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        if (dto.getEmployeeId() == null) {
            throw new IllegalArgumentException("员工ID不能为空");
        }
        log.debug("请求参数验证通过");
    }

    /**
     * 查找AI工具配置
     * 如果dto中的metadata包含isModel=true，则查询模型类型的工具
     * 否则查询非模型类型的工具
     */
    private AiTool findAiToolByKey(String toolKey, AiInvocationRequestDTO dto) {
        // 检查metadata中是否包含isModel字段
        boolean isModel = false;
        if (dto.getMetadata() != null && dto.getMetadata().containsKey("isModel")) {
            Object isModelValue = dto.getMetadata().get("isModel");
            // 处理不同类型的isModel值
            if (isModelValue instanceof Boolean) {
                isModel = (Boolean) isModelValue;
            } else if (isModelValue instanceof String) {
                isModel = Boolean.parseBoolean((String) isModelValue);
            }
            log.debug("从metadata中获取isModel值: {}", isModel);
        }

        // 根据isModel值选择不同的查询方法
        if (isModel) {
            return toolRepo
                .findByToolKeyAndIsDeletedFalse(toolKey)
                .filter(tool -> tool.getIsModel() != null && tool.getIsModel())
                .orElseThrow(() -> new AiInvocationException("未找到模型配置: " + toolKey, toolKey, "MODEL_NOT_FOUND"));
        } else {
            return toolRepo
                .findByToolKeyAndIsDeletedFalseAndIsModelFalse(toolKey)
                .orElseThrow(() -> new AiInvocationException("未找到工具配置: " + toolKey, toolKey, "TOOL_NOT_FOUND"));
        }
    }

    /**
     * 创建AI请求记录
     */
    private AiRequest createAiRequest(AiInvocationRequestDTO dto, AiTool aiTool) {
        AiRequest req = new AiRequest();
        req.setToolType(dto.getToolKey());
        req.setPrompt(dto.getPrompt());
        req.setResponse(""); // 初始化为空字符串
        req.setTool(aiTool); // 设置工具关联

        // 设置必填的时间字段
        Instant now = Instant.now();
        req.setCreatedAt(now);
        req.setUpdatedAt(now);
        req.setRequestTime(now);

        // 设置必填的状态和版本字段
        req.setStatus(RequestStatus.PROCESSING);
        req.setVersion(1);

        // 设置必填的ID字段
        req.setTenantId(dto.getTenantId());
        req.setEmployeeId(dto.getEmployeeId());

        // 设置软删除标志
        req.setIsDeleted(false);

        // 设置可选的元数据
        try {
            req.setMetadata(dto.getMetadata() == null ? null : objectMapper.writeValueAsString(dto.getMetadata()));
        } catch (JsonProcessingException e) {
            log.warn("序列化元数据失败: {}", e.getMessage());
            req.setMetadata(null);
        }

        return req;
    }

    /**
     * 更新请求为成功状态
     */
    private void updateRequestWithSuccess(AiRequest req, AiWorkflowResult result) {
        // 截断响应内容，确保不超过合理限制
        String content = result.getContent();
        // TEXT 字段可以存储更多内容，但仍然需要合理限制以避免过大的数据
        // 设置为 50KB 的限制，对于大多数 AI 响应来说应该足够了
        if (content != null && content.length() > 50000) {
            content = content.substring(0, 49997) + "...";
            log.warn("AI响应内容过长，已截断。原长度: {}, 截断后长度: {}", result.getContent().length(), content.length());
        }
        //这里要对返回的内容进行敏感词过滤
        if (content != null && !content.isEmpty()) {
            log.debug("对AI响应内容进行敏感词过滤，处理前内容长度: {}", content.length());
            content = sensitiveWordFilterService.filterContent(content);
            log.debug("敏感词过滤完成，处理后内容长度: {}", content.length());
        }

        req.setResponse(content);
        req.setStatus(RequestStatus.SUCCESS);
        req.setResponseTime(Instant.now());
        req.setUpdatedAt(Instant.now());
        requestRepo.save(req);
    }

    /**
     * 更新请求为失败状态
     */
    private void updateRequestWithError(AiRequest req, Exception e) {
        // 截断错误信息，确保不超过合理限制
        String errorMessage = e.getMessage();
        // TEXT 字段可以存储更多内容，但错误信息通常不会太长
        // 设置为 10KB 的限制，对于错误信息来说应该足够了
        if (errorMessage != null && errorMessage.length() > 10000) {
            errorMessage = errorMessage.substring(0, 9997) + "...";
            log.warn("错误信息过长，已截断。原长度: {}, 截断后长度: {}", e.getMessage().length(), errorMessage.length());
        }

        req.setStatus(RequestStatus.FAILED);
        req.setErrorMessage(errorMessage);
        req.setResponseTime(Instant.now());
        req.setUpdatedAt(Instant.now());
        requestRepo.save(req);
    }
}
